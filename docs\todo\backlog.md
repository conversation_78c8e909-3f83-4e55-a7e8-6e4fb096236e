### Prompt 19: Other todos

### On login page:

Add logo
Change font style for "Dr. Muscle X - Login to continue your workout" to a modern pair that matches the "X" angle and slogan.
Replace "Login to continue your workout" with "World's Fastest AI Personal Trainer"

### On exercise page:

Reps remove 100 or less

Create .husky\pre-push file with:
#!/usr/bin/env sh
. "$(dirname -- "$0")/\_/husky.sh"

npx playwright test

Fix Claude Code Github actions (https://github.com/dr-muscle/DrMuscleWebApp/actions/runs/***********/job/***********)

Update home (program) page to add an horizontal module at the top, similar to top card in Dr. Muscle. This can be displayed quickly using Endpoint: POST /api/Account/GetUserInfoPyramid, make the loading experience smoother for home page.

Another way to make the loading experience smoother would be to show the tips overlays, similar to what we have on tap Start workout in the mobile app.

Bring back recovery card at the top of program page. It's commented out.

TDD
Chat with AI to see how best to implement
Create testing suite to run every time? Where?
Add to claude.md

Terra: install <PERSON>wright
install Playwright browsers
Readd terragon-setup.sh

<PERSON>'s comments
UI

- Yellow gradient
- Blue gradient

staging.dr-muscle.com

Commit 26109a4 was working

Claude Playwright MCP

Once workouts are loading, harden with tests etc so that a random change does not break them.
Write the steps for the test myself, asking AI to codify it. Then ask AI how to run it every time. Prevent commits when the test is failing. Then another for login, and another for exercise recos.

Terragon: Better PRs

Design: generate other color palettes and layout principles for mobile-first responsive design

Big cleanup: Remove "backward compatibility" with older implementations (this is not in production).

✅ Removed complex transitions - No more gold transitions, progressive loading, cross-fade animations

Commit 13e2aa0 IS WORKING -- NO RACE CONDITIONS!!!

Have AI describe in detail how workouts work, for Opus to implement in Web app. Create implementation plans.

Offline mode

Swaps are saved locally (not in database). Refactor swap feature to use same endpoints as edit workout. Swapping an exercise would actually remove it from the workout and add the new one instead, in the same position in the list (same order). https://www.terragonlabs.com/task/53723292-1741-4dd9-903b-b76719529b76

Add this to exercise page for better UX
Let's warm up:
Work sets:
Remove explainer bos entirely?

Latest working:
Still working in terragon/add-set-type-labels
fix/sets-display-numbering is close

Would need changes to support assisted pullups/dips
handle negative weights for assisted exercises

## Exercise Quick Navigation (Bottom Bar with Exercise Bubbles)

**Context**: This feature was temporarily removed from PR #334 to simplify scope. It provides a bottom navigation bar with pill-shaped exercise buttons for quick workout navigation.

**Component**: `ExerciseQuickNav.tsx`

### Key Features:

- Fixed bottom navigation with horizontal scrolling exercise "bubbles"
- Each exercise shown as `rounded-full` pill-shaped button
- Active exercise highlighted with gold gradient
- Completed exercises show check icon and different styling
- Progress bar showing workout completion percentage
- Haptic feedback on interactions
- Mobile-first design with touch-friendly 44px+ targets

### Implementation Details:

```typescript
'use client'

import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Check, Circle } from 'lucide-react'
import type { ExerciseModel } from '@/types'
import { vibrate } from '@/utils/haptics'

interface ExerciseQuickNavProps {
  exercises: ExerciseModel[]
  exerciseId: number
}

export function ExerciseQuickNav({
  exercises,
  exerciseId,
}: ExerciseQuickNavProps) {
  const router = useRouter()

  const handleExerciseClick = (targetExerciseId: number) => {
    if (targetExerciseId === exerciseId) return

    vibrate('light')
    router.push(`/workout/exercise-v2/${targetExerciseId}`)
  }

  return (
    <>
      {/* Fixed bottom navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-40 bg-surface-secondary border-t border-surface-tertiary pb-safe">
        <div className="p-8">
          <div className="flex gap-2 overflow-x-auto scrollbar-hide">
            {exercises.map((exercise, index) => {
              const isActive = exercise.Id === exerciseId
              const isCompleted = exercise.IsFinished || false

              return (
                <motion.button
                  key={exercise.Id}
                  onClick={() => handleExerciseClick(exercise.Id || 0)}
                  className={`
                    flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium
                    transition-all duration-200 min-w-[100px]
                    ${(() => {
                      if (isActive)
                        return 'bg-gradient-to-r from-brand-gold-start to-brand-gold-end text-text-inverse'
                      if (isCompleted)
                        return 'bg-surface-primary text-text-primary border border-brand-primary'
                      return 'bg-surface-primary text-text-secondary border border-surface-tertiary'
                    })()}
                  `}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="flex items-center gap-2">
                    {isCompleted ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Circle
                        className={`w-4 h-4 ${isActive ? 'fill-current' : ''}`}
                      />
                    )}
                    <span className="truncate max-w-[120px]">
                      {exercise.Label || `Exercise ${index + 1}`}
                    </span>
                  </div>
                </motion.button>
              )
            })}
          </div>

          {/* Progress indicator */}
          <div className="mt-2">
            <div className="h-1 bg-surface-tertiary rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-brand-primary"
                initial={{ width: 0 }}
                animate={{
                  width: `${
                    (exercises.filter((e) => e.IsFinished).length /
                      exercises.length) *
                    100
                  }%`,
                }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Spacer to prevent content overlap */}
      <div data-spacer="bottom-nav" className="pb-20" />
    </>
  )
}

// Add custom scrollbar hide CSS
const style = `
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
`

if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = style
  document.head.appendChild(styleSheet)
}
```

### Usage Integration:

```typescript
// In ExercisePageV2Client.tsx
import { ExerciseQuickNav } from '@/components/workout-v2/ExerciseQuickNav'

// Usage:
<ExerciseQuickNav exercises={exercises} exerciseId={exerciseId} />
```

### Test Coverage:

- Component UI tests: `ExerciseQuickNav.test.tsx`
- Bottom position tests: `ExerciseQuickNav.bottom-position.test.tsx`

### Design Notes:

- Uses gold gradient (`from-brand-gold-start to-brand-gold-end`) for active exercise
- Framer Motion for smooth hover/tap animations
- Fixed bottom positioning with safe area padding
- Horizontal scroll with hidden scrollbars for mobile UX

Last working commit: 24efc8600e2d9d36d4459c18542875e1a35ce637

---

Last passing critical E2E tests passed: https://github.com/dr-muscle/DrMuscleWebApp/pull/404
https://github.com/dr-muscle/DrMuscleWebApp/pull/401
