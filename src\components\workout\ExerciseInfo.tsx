'use client'

import { useEffect } from 'react'
import { formatRecommendationWeight } from '@/utils/weightUtils'
import type { ExerciseModel, RecommendationModel } from '@/types'

interface ExerciseInfoProps {
  currentExercise?: ExerciseModel
  recommendation?: RecommendationModel | null
  performancePercentage: number | null
  oneRMProgress?: string
  lastTimeInfo?: string
}

export function ExerciseInfo({
  currentExercise,
  recommendation,
  performancePercentage,
  oneRMProgress,
  lastTimeInfo,
}: ExerciseInfoProps) {
  // Log recommendation data for debugging (only when data changes)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[ExerciseInfo] Recommendation data:', {
        exerciseId: currentExercise?.Id,
        exerciseLabel: currentExercise?.Label,
        isBodyweight: currentExercise?.IsBodyweight,
        hasRecommendation: !!recommendation,
        weight: recommendation?.Weight,
        weightLb: recommendation?.Weight?.Lb,
        weightIsNull: recommendation?.Weight === null,
        weightIsZero: recommendation?.Weight?.Lb === 0,
        reps: recommendation?.Reps,
        series: recommendation?.Series,
      })
    }
  }, [
    currentExercise?.Id,
    currentExercise?.Label,
    currentExercise?.IsBodyweight,
    recommendation,
  ])

  return (
    <div
      className="px-4 py-6 bg-bg-secondary border-b border-brand-primary/10"
      data-testid="recommendations"
    >
      <div className="space-y-1">
        {/* Target info */}
        <p className="text-text-secondary">
          {recommendation ? (
            <>
              Recommended: {recommendation.Series || 3} x{' '}
              {recommendation.Reps || 12}
              {!currentExercise?.IsBodyweight &&
                recommendation.Weight &&
                recommendation.Weight.Lb &&
                recommendation.Weight.Lb > 0 &&
                ` @ ${formatRecommendationWeight(recommendation.Weight, 'lbs', recommendation.Increments?.Lb)}`}
            </>
          ) : (
            'Loading recommendation...'
          )}
        </p>

        {/* Show additional message if weight is missing for weighted exercises */}
        {!currentExercise?.IsBodyweight &&
          !currentExercise?.IsTimeBased &&
          (!recommendation?.Weight || recommendation.Weight.Lb === 0) &&
          (!recommendation?.RecommendationInKg ||
            recommendation.RecommendationInKg === 0) && (
            <p className="text-sm text-warning">No weight recommendation</p>
          )}

        {/* Performance percentage */}
        {performancePercentage !== null && (
          <p
            className={`font-medium ${
              performancePercentage >= 0 ? 'text-success' : 'text-error'
            }`}
          >
            {performancePercentage >= 0 ? '+' : ''}
            {performancePercentage}%
          </p>
        )}

        {/* 1RM Progress Display */}
        {oneRMProgress && (
          <div
            data-testid="one-rm-display"
            className="bg-brand-primary/10 p-3 rounded-lg border border-brand-primary/20 mt-2"
          >
            <div className="text-lg font-bold text-brand-primary">
              1RM Progress: {oneRMProgress}
            </div>
          </div>
        )}

        {/* Last Time Info */}
        {lastTimeInfo && (
          <p className="text-sm text-text-secondary">{lastTimeInfo}</p>
        )}
      </div>
    </div>
  )
}
