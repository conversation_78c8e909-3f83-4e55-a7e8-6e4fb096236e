import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function middleware(_request: NextRequest) {
  const response = NextResponse.next()
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Content Security Policy
  // TODO: Remove 'unsafe-inline' and 'unsafe-eval' after refactoring inline scripts
  // For now, we need them for Next.js hydration and some third-party scripts
  // NOTE: Browser extension errors (e.g., LastPass "Invalid frameId") are expected and harmless
  const frameSrcDomains = [
    "'self'",
    'https://accounts.google.com',
    'https://appleid.apple.com',
    'https://drmuscle-4a08d.firebaseapp.com',
    // Allow Vercel Live feedback widget in all environments
    // Vercel automatically injects their feedback widget in all deployments
    'https://vercel.live',
  ].join(' ')

  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://apis.google.com https://appleid.cdn-apple.com https://vercel.live",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data:",
    "connect-src 'self' https://drmuscle.azurewebsites.net https://www.google-analytics.com https://vitals.vercel-insights.com https://region1.google-analytics.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://apis.google.com",
    `frame-src ${frameSrcDomains}`,
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    'block-all-mixed-content',
    // Skip upgrade-insecure-requests in development to prevent HTTPS upgrade issues
    ...(isDevelopment ? [] : ['upgrade-insecure-requests']),
    'report-uri /api/csp-report',
  ].join('; ')

  // Security headers
  response.headers.set('Content-Security-Policy', csp)
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=()'
  )

  // HSTS header for secure transmission (2 years, includeSubDomains, preload)
  // Skip HSTS in development to prevent HTTPS enforcement issues
  if (!isDevelopment) {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=63072000; includeSubDomains; preload'
    )
  }

  // Remove potentially dangerous headers
  response.headers.delete('X-Powered-By')

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (robots.txt, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
