{"permissions": {"allow": ["Bash(npx create-next-app:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm test:*)", "Bash(npm install:*)", "Bash(npm run lint)", "Bash(npm run format:*)", "Bash(npm run typecheck:*)", "Bash(npm run build:*)", "Bash(git add:*)", "Bash(git rm:*)", "Bash(git commit:*)", "Bash(node:*)", "Bash(npx vitest:*)", "Bash(./node_modules/.bin/vitest:*)", "Bash(npm run test:ci:*)", "Bash(npx next:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(command -v:*)", "Bash(export NVM_DIR=\"$HOME/.nvm\")", "Bash([ -s \"$NVM_DIR/nvm.sh\" ])", "Bash(. \"$NVM_DIR/nvm.sh\")", "Bash(nvm install:*)", "Bash(./node_modules/.bin/next:*)", "Bash(npm rebuild:*)", "Bash(ln:*)", "Bash(npm cache clean:*)", "Bash(npm ls:*)", "Bash(npm --version)", "Bash(nvm use:*)", "Bash(grep:*)", "Bash(npm run prepare:*)", "Bash(NEXT_TELEMETRY_DISABLED=1 npm run build)", "Bash(timeout 300 npm install --legacy-peer-deps --no-audit --progress=false)", "Bash(npm ci:*)", "Bash(npm run test:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(timeout 10 npm run dev:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(npm run lint:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(sed:*)", "Bash(git push:*)", "Bash(gh auth:*)", "Bash(git config:*)", "Bash(gh api:*)", "Bash(gh repo view:*)", "Bash(npx vercel:*)", "Bash(git log:*)", "<PERSON><PERSON>(gh run list:*)", "WebFetch(domain:x.dr-muscle.com)", "WebFetch(domain:dr-muscle.com)", "Bash(npm run type-check:*)", "<PERSON><PERSON>(npx playwright install:*)", "Bash(PLAYWRIGHT_BASE_URL=https://x.dr-muscle.com npx playwright test tests/e2e/auth-flow.spec.ts --project=\"chromium\" --reporter=list)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(TEST_EMAIL=<EMAIL> TEST_PASSWORD=testpassword npx playwright test tests/e2e/workout-display.spec.ts --reporter=list --headed=false)", "Bash(TEST_EMAIL=<EMAIL> TEST_PASSWORD=testpassword npx playwright test tests/e2e/workout-display.spec.ts --reporter=list)", "Bash(rg:*)", "Bash(gh workflow:*)", "<PERSON>sh(git check-ignore:*)", "Bash(npm run dev:*)", "Bash(npm run:*)", "Bash(git reset:*)", "Bash(PLAYWRIGHT_BASE_URL=http://localhost:3000 npm run test:e2e -- tests/e2e/program-overview.spec.ts --headed=false)", "Bash(PLAYWRIGHT_BASE_URL=http://localhost:3001 npm run test:e2e -- tests/e2e/program-overview.spec.ts --project=\"Mobile Chrome\" --reporter=list)", "<PERSON><PERSON>(killall:*)", "Bash(kill:*)", "<PERSON><PERSON>(cat:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(git merge:*)", "Bash(./node_modules/.bin/eslint:*)", "Bash(git cherry-pick:*)", "Bash(git rebase:*)", "WebFetch(domain:localhost)", "Bash(git fetch:*)", "Bash(git stash:*)", "Bash(npx ts-node:*)", "<PERSON><PERSON>(chmod:*)", "Bash(PLAYWRIGHT_BASE_URL=http://localhost:3000 npx playwright test e2e/workout-loading-fix.spec.ts --reporter=list)", "Bash(npx lint-staged:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__Context-Engineer__plan_feature"], "deny": []}}