/**
 * Custom hook for exercise page initialization logic
 * Extracted from ExercisePageClient to reduce file size and improve maintainability
 */

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useAuthStore } from '@/stores/authStore'
import { debugLog } from '@/utils/debugLog'
import { RecommendationLoadingCoordinator } from '@/utils/RecommendationLoadingCoordinator'

export interface ExercisePageInitializationState {
  isInitializing: boolean
  loadingError: Error | null
  retryInitialization: () => Promise<void>
  retryCount: number
}

export function useExercisePageInitialization(
  exerciseId: number
): ExercisePageInitializationState {
  const router = useRouter()
  const { token, isAuthenticated } = useAuthStore()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  const {
    todaysWorkout,
    isLoadingWorkout,
    startWorkout,
    exercises,
    workoutSession,
    loadRecommendation,
    updateExerciseWorkSets,
  } = useWorkout()

  const {
    setCurrentExerciseById,
    loadingStates,
    getCachedExerciseRecommendation,
  } = useWorkoutStore()

  // Check auth state early
  useEffect(() => {
    if (!token || !isAuthenticated) {
      debugLog(
        '🚫 [useExercisePageInitialization] No auth token, redirecting to login',
        {
          hasToken: !!token,
          isAuthenticated,
        }
      )
      router.replace('/login')
      setIsInitializing(false)
    }
  }, [token, isAuthenticated, router])

  // Retry function for manual initialization
  const retryInitialization = async () => {
    try {
      setIsInitializing(true)
      setLoadingError(null)
      setRetryCount((prev) => prev + 1)

      // Check auth before retrying
      if (!token || !isAuthenticated) {
        debugLog('🚫 [retryInitialization] No auth token, redirecting to login')
        router.replace('/login')
        return
      }

      // Start workout if needed
      if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
        const workoutGroup = todaysWorkout[0]
        const workout = workoutGroup?.WorkoutTemplates?.[0]

        if (workout) {
          const result = await startWorkout(todaysWorkout)
          if (!result.success) {
            debugLog.error('Failed to start workout')
            router.replace('/workout')
            return
          }
        } else {
          router.replace('/workout')
          return
        }
      }

      // Set current exercise and load recommendations
      if (exerciseId) {
        // Skip validation if exercises haven't been loaded yet or if still loading workout
        // Also skip if we don't have a workout session yet (still initializing)
        // IMPORTANT: Keep initializing state until we have confirmed workout data
        if (
          !exercises ||
          exercises.length === 0 ||
          isLoadingWorkout ||
          !workoutSession
        ) {
          debugLog.log(
            '[useExercisePageInitialization] Exercises not loaded yet, workout still loading, or no session, skipping validation',
            {
              hasExercises: !!exercises,
              exercisesLength: exercises?.length || 0,
              isLoadingWorkout,
              hasWorkoutSession: !!workoutSession,
            }
          )
          // Don't mark as complete yet - we're still waiting for data
          return
        }

        // First validate that the exercise exists in the workout
        debugLog.log('[useExercisePageInitialization] Validating exercise:', {
          exerciseId,
          exercisesLength: exercises?.length,
          exerciseIds: exercises?.map((ex) => ex.Id),
          exerciseIdType: typeof exerciseId,
        })

        const exercise = exercises?.find(
          (ex) => Number(ex.Id) === Number(exerciseId)
        )

        if (!exercise) {
          // Exercise not found in workout
          debugLog.error(
            `Exercise ${exerciseId} not found in workout exercises`,
            {
              exerciseId,
              exerciseIdType: typeof exerciseId,
              availableExercises: exercises?.map((ex) => ({
                id: ex.Id,
                label: ex.Label,
                idType: typeof ex.Id,
              })),
            }
          )
          throw new Error(`Exercise ${exerciseId} not found in workout`)
        }

        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          // Check with coordinator before loading
          try {
            const coordinator = RecommendationLoadingCoordinator.getInstance()
            if (coordinator.canStartLoading(exerciseId)) {
              // Start loading with 30 second timeout to prevent infinite skeletons
              coordinator.startLoading(exerciseId, { timeout: 30000 })
              loadRecommendation(exerciseId, exercise.Label || 'Exercise')
            } else {
              debugLog.log(
                '[useExercisePageInitialization] Coordinator blocked loading - already in progress',
                { exerciseId }
              )
            }
          } catch (error) {
            // If coordinator fails, proceed with loading anyway
            debugLog.error(
              '[useExercisePageInitialization] Coordinator error, proceeding with load',
              error
            )
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        if (!exercise.sets || exercise.sets.length === 0) {
          updateExerciseWorkSets(exerciseId, [])
        }
      }
    } catch (error) {
      debugLog.error('Failed to retry initialization:', error)

      // Check if it's an auth error or if we've exceeded retry limit
      const isAuthError =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'status' in error.response &&
        error.response.status === 401

      // Auto-login fallback after 3 failed retries or immediate auth error
      if (isAuthError || retryCount >= 3) {
        debugLog(
          '🚫 [retryInitialization] Auth error or retry limit exceeded, redirecting to login',
          {
            isAuthError,
            retryCount,
          }
        )
        router.replace('/login')
        return
      }

      setLoadingError(
        error instanceof Error
          ? error
          : new Error('Failed to retry initialization')
      )
    } finally {
      setIsInitializing(false)
    }
  }

  // Effect 1: Start workout if needed
  useEffect(() => {
    // Skip if no auth
    if (!token || !isAuthenticated) {
      return
    }

    async function startWorkoutIfNeeded() {
      try {
        setLoadingError(null)

        // Guard: Skip workout start if session already exists
        if (workoutSession) {
          debugLog(
            '✅ [ExercisePageClient] Workout session already exists, skipping workout start',
            {
              sessionId: workoutSession.id,
            }
          )
          // Don't set isInitializing to false here - let the exercise setup effect handle it
          return
        }

        if (todaysWorkout && !isLoadingWorkout) {
          debugLog('🚀 [ExercisePageClient] Starting workout...', {
            todaysWorkout,
          })

          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            const result = await startWorkout(todaysWorkout)
            if (result.success) {
              debugLog('✅ [ExercisePageClient] Workout started successfully')
            } else {
              debugLog.error('Failed to start workout in initialization')
            }
          } else {
            debugLog.error(
              '❌ [ExercisePageClient] No workout template found, redirecting...'
            )
            router.replace('/workout')
          }
        }
      } catch (error: unknown) {
        debugLog.error('Failed to start workout:', error)

        // Check if it's an auth error
        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          (error.response as { status?: number })?.status === 401
        ) {
          debugLog('🚫 [startWorkoutIfNeeded] Got 401, redirecting to login')
          router.replace('/login')
          return
        }

        setLoadingError(
          error instanceof Error ? error : new Error('Failed to start workout')
        )
      }
    }

    startWorkoutIfNeeded()
  }, [
    workoutSession,
    todaysWorkout,
    isLoadingWorkout,
    startWorkout,
    router,
    token,
    isAuthenticated,
  ])

  // Effect 2: Set current exercise and load recommendations
  useEffect(() => {
    // Skip if no auth
    if (!token || !isAuthenticated) {
      return
    }

    async function setupExercise() {
      try {
        if (!exerciseId) return

        debugLog('🎯 [ExercisePageClient] Setting up exercise', {
          exerciseId,
          hasExercises: !!exercises,
          exercisesCount: exercises?.length || 0,
          hasWorkoutSession: !!workoutSession,
          isInitializing,
        })

        // Skip validation if exercises haven't been loaded yet or if still loading workout
        // Also skip if we're still in the process of starting a workout (no session yet)
        // CRITICAL: Don't mark initialization complete until we have valid data
        if (
          !exercises ||
          exercises.length === 0 ||
          isLoadingWorkout ||
          (!workoutSession && todaysWorkout)
        ) {
          debugLog.log(
            '⏳ [ExercisePageClient] Exercises not loaded yet, workout still loading, or session being created, waiting...',
            {
              hasExercises: !!exercises,
              exercisesLength: exercises?.length || 0,
              isLoadingWorkout,
              hasWorkoutSession: !!workoutSession,
              hasTodaysWorkout: !!todaysWorkout,
            }
          )
          // Keep isInitializing true - don't mark complete yet
          return
        }

        // First validate that the exercise exists in the workout
        const exercise = exercises?.find(
          (ex) => Number(ex.Id) === Number(exerciseId)
        )

        if (!exercise) {
          debugLog.error(
            '❌ [ExercisePageClient] Exercise not found in exercises list',
            {
              exerciseId,
              availableIds: exercises?.map((ex) => ex.Id),
            }
          )
          throw new Error(`Exercise ${exerciseId} not found in workout`)
        }

        // Set current exercise by ID
        setCurrentExerciseById(exerciseId)

        // Check if recommendation is loaded for this exercise
        const hasRecommendation = getCachedExerciseRecommendation(exerciseId)
        const isLoadingRecommendation = loadingStates.get(exerciseId)

        debugLog('💭 [ExercisePageClient] Recommendation status', {
          exerciseId,
          hasRecommendation: !!hasRecommendation,
          isLoadingRecommendation,
        })

        // If no recommendation and not loading, trigger loading
        if (!hasRecommendation && !isLoadingRecommendation) {
          debugLog('📡 [ExercisePageClient] Loading recommendation...', {
            exerciseId,
            exerciseLabel: exercise.Label,
          })

          // Check with coordinator before loading
          try {
            const coordinator = RecommendationLoadingCoordinator.getInstance()
            if (coordinator.canStartLoading(exerciseId)) {
              // Start loading with 30 second timeout to prevent infinite skeletons
              coordinator.startLoading(exerciseId, { timeout: 30000 })
              loadRecommendation(exerciseId, exercise.Label || 'Exercise')
            } else {
              debugLog.log(
                '[ExercisePageClient] Coordinator blocked loading - already in progress',
                { exerciseId }
              )
            }
          } catch (error) {
            // If coordinator fails, proceed with loading anyway
            debugLog.error(
              '[ExercisePageClient] Coordinator error, proceeding with load',
              error
            )
            loadRecommendation(exerciseId, exercise.Label || 'Exercise')
          }
        }

        // Pre-load recommendation if not already loaded using alternative method
        if (!exercise.sets || exercise.sets.length === 0) {
          debugLog(
            '🔧 [ExercisePageClient] Updating exercise work sets to empty array'
          )
          updateExerciseWorkSets(exerciseId, [])
        }
      } catch (error: unknown) {
        debugLog.error('Failed to setup exercise:', error)

        // Check if it's an auth error
        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          (error.response as { status?: number })?.status === 401
        ) {
          debugLog('🚫 [setupExercise] Got 401, redirecting to login')
          router.replace('/login')
          return
        }

        setLoadingError(
          error instanceof Error ? error : new Error('Failed to setup exercise')
        )
        // Mark as complete on error
        setIsInitializing(false)
      } finally {
        // Only mark initialization as complete if we have validated the exercise
        if (exercises && exercises.length > 0 && workoutSession) {
          setIsInitializing(false)
        }
      }
    }

    setupExercise()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    exerciseId,
    exercises?.length, // Only depend on length to avoid array reference changes
    workoutSession, // Add workoutSession to ensure setup runs after session is ready
    isLoadingWorkout, // Add loading state to ensure we re-check when loading completes
    todaysWorkout, // Add todaysWorkout to re-run when workout data is available
    token, // Add auth dependencies
    isAuthenticated,
    // Removed function dependencies that are recreated on every render
    // setCurrentExerciseById, loadingStates, loadRecommendation, updateExerciseWorkSets
    // These are stable functions from the store and don't need to be in dependencies
  ])

  return {
    isInitializing,
    loadingError,
    retryInitialization,
    retryCount,
  }
}
