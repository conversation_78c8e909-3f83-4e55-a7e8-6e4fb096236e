/**
 * Constants for the workout store
 */

import type { CachedAPIData, CacheStats } from './types'

// Constants
export const CACHE_VERSION = 3 // Incremented to support exercise swaps
export const MAX_RECOMMENDATIONS = 50

// Cache expiry times in milliseconds
export const CACHE_EXPIRY = {
  userProgramInfo: 24 * 60 * 60 * 1000, // 24 hours
  userWorkouts: 24 * 60 * 60 * 1000, // 24 hours
  todaysWorkout: 24 * 60 * 60 * 1000, // 24 hours
  exerciseRecommendation: 60 * 60 * 1000, // 1 hour
}

export const initialCachedData: CachedAPIData = {
  userProgramInfo: null,
  userWorkouts: null,
  todaysWorkout: null,
  exerciseRecommendations: {},
  lastUpdated: {
    userProgramInfo: 0,
    userWorkouts: 0,
    todaysWorkout: 0,
    exerciseRecommendations: {},
  },
}

export const initialCacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  hitRate: 0,
  operationCount: 0,
  averageLatency: 0,
  totalLatency: 0,
  totalSize: 0,
  itemCount: 0,
  oldestDataAge: 0,
  freshDataCount: 0,
  staleDataCount: 0,
  hydrationTime: 0,
}

export const initialState = {
  currentWorkout: null,
  currentProgram: null,
  exercises: [],
  currentExerciseIndex: 0,
  currentSetIndex: 0,
  workoutSession: null,
  isLoading: false,
  error: null,
  currentSetData: {},
  loadingStates: new Map(),
  errors: new Map(),
  exerciseSwaps: {},
  previewExerciseSkips: new Set<number>(),
  restTimerState: { isActive: false, duration: 0 },
  cachedData: initialCachedData,
  hasHydrated: false,
  cacheVersion: CACHE_VERSION,
  cacheStats: initialCacheStats,
  onExerciseStatusUpdate: undefined,
  lastActiveTimestamp: undefined,
}
