import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ExerciseInfo } from '../ExerciseInfo'
import type { ExerciseModel, RecommendationModel } from '@/types'

describe('ExerciseInfo', () => {
  const mockExercise: ExerciseModel = {
    Id: 1,
    Label: 'Bench Press',
    IsBodyweight: false,
    IsTimeBased: false,
    Timer: 0,
    SetStyle: 'Normal',
    IsFlexibility: false,
  }

  const mockRecommendation: RecommendationModel = {
    Series: 3,
    Reps: 10,
    Weight: {
      Lb: 135,
      Kg: 61.2,
    },
    WarmupsCount: 2,
  }

  it('should display recommendations section', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        performancePercentage={null}
      />
    )

    expect(screen.getByTestId('recommendations')).toBeInTheDocument()
  })

  it('should display recommended weight and reps for weighted exercises', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Recommended: 3 x 10/)).toBeInTheDocument()
    expect(screen.getByText(/@ 135 lbs/)).toBeInTheDocument()
  })

  it('should round recommendation weight based on WeightIncrement', () => {
    const recommendationWithIncrement: RecommendationModel = {
      Series: 3,
      Reps: 10,
      Weight: {
        Lb: 102.7654321,
        Kg: 46.58372,
      },
      Increments: {
        Lb: 5,
        Kg: 2.5,
      },
      WarmupsCount: 2,
    } as RecommendationModel

    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendationWithIncrement}
        performancePercentage={null}
      />
    )

    // Should round to 105 lbs based on WeightIncrement of 5
    expect(screen.getByText(/@ 105 lbs/)).toBeInTheDocument()
    // Should NOT show many decimals
    expect(screen.queryByText(/102.7654321/)).not.toBeInTheDocument()
  })

  it('should display only reps for bodyweight exercises', () => {
    const bodyweightExercise = { ...mockExercise, IsBodyweight: true }
    render(
      <ExerciseInfo
        currentExercise={bodyweightExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Recommended: 3 x 10/)).toBeInTheDocument()
    expect(screen.queryByText(/135 lbs/)).not.toBeInTheDocument()
  })

  it('should display duration for time-based exercises', () => {
    const timeBasedExercise = { ...mockExercise, IsTimeBased: true, Timer: 60 }
    render(
      <ExerciseInfo
        currentExercise={timeBasedExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    // Time-based exercises still show Target
    expect(screen.getByText(/Recommended: 3 x 10/)).toBeInTheDocument()
  })

  it('should display "No weight recommendation" when weight is missing or 0', () => {
    const recommendationNoWeight = {
      ...mockRecommendation,
      Weight: { Lb: 0, Kg: 0 },
    }
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={recommendationNoWeight}
        performancePercentage={null}
      />
    )

    expect(screen.getByText(/Recommended: 3 x 10/)).toBeInTheDocument()
    expect(screen.getByText(/No weight recommendation/)).toBeInTheDocument()
  })

  it('should display performance percentage when provided', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={5.5}
      />
    )

    expect(screen.getByText('+5.5%')).toBeInTheDocument()
    expect(screen.getByText('+5.5%')).toHaveClass('text-success')
  })

  it('should display negative performance percentage in red', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={-3.2}
      />
    )

    expect(screen.getByText('-3.2%')).toBeInTheDocument()
    expect(screen.getByText('-3.2%')).toHaveClass('text-error')
  })

  it('should have data-testid="recommendations"', () => {
    render(
      <ExerciseInfo
        currentExercise={mockExercise}
        recommendation={mockRecommendation}
        performancePercentage={null}
      />
    )

    expect(screen.getByTestId('recommendations')).toBeInTheDocument()
  })
})
