import { render } from '@testing-library/react'
import { vi, describe, it, expect } from 'vitest'
import { ExercisePageClient } from '../ExercisePageClient'

// Mock the ExercisePageClient dependencies to avoid complex setup
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(() => ({
    isLoadingWorkout: false,
    workoutError: null,
    exercises: [{ Id: 30431, Label: 'Test Exercise' }],
    workoutSession: { id: 1 },
  })),
}))

vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: vi.fn(() => ({
    loadingStates: new Map(),
  })),
}))

vi.mock('@/hooks/useExercisePageInitialization', () => ({
  useExercisePageInitialization: vi.fn(() => ({
    isInitializing: false,
    loadingError: null,
    retryInitialization: vi.fn(),
  })),
}))

vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: vi.fn(() => ({
    recommendation: { id: 1, exerciseId: 30431 },
  })),
}))

vi.mock('next/navigation', () => ({
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
  })),
}))

vi.mock('@/components/workout/SetScreenWithGrid', () => ({
  SetScreenWithGrid: vi.fn(({ exerciseId }) => (
    <div data-testid="exercise-client">Exercise {exerciseId}</div>
  )),
}))

describe('ExercisePage Navigation', () => {
  it('should render ExercisePageClient with correct exerciseId', () => {
    // Given: A valid exercise ID
    const exerciseId = 30431

    // When: Rendering the ExercisePageClient
    const { getByTestId } = render(
      <ExercisePageClient exerciseId={exerciseId} />
    )

    // Then: Should render with the correct ID
    expect(getByTestId('exercise-client')).toHaveTextContent('Exercise 30431')
  })

  it('should handle different exercise IDs correctly', () => {
    // Given: A different exercise ID
    const exerciseId = 12345

    // When: Rendering the ExercisePageClient
    const { getByTestId } = render(
      <ExercisePageClient exerciseId={exerciseId} />
    )

    // Then: Should render with the correct ID
    expect(getByTestId('exercise-client')).toHaveTextContent('Exercise 12345')
  })
})
