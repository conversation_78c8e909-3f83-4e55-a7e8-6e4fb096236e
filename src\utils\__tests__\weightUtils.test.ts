import { describe, it, expect } from 'vitest'
import {
  formatWeight,
  calculatePercentageChange,
  roundWeight,
  roundToNearestIncrement,
  truncateDecimal,
  formatWeightForDisplay,
  formatRecommendationWeight,
} from '../weightUtils'
import type { MultiUnityWeight } from '@/types'

describe('weightUtils', () => {
  describe('formatWeight', () => {
    it('should format weight in pounds', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'lbs')).toBe('100 lbs')
    })

    it('should format weight in kilograms', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, 'kg')).toBe('45.36 kg')
    })

    it('should handle decimal values', () => {
      const weight: MultiUnityWeight = { Lb: 102.5, Kg: 46.5 }
      expect(formatWeight(weight, 'lbs')).toBe('102.5 lbs')
    })

    it('should handle null weight', () => {
      expect(formatWeight(null as any)).toBe('0 lbs')
    })

    it('should format with 0.## pattern (mobile format)', () => {
      const weight1: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight1, 'lbs')).toBe('100 lbs')

      const weight2: MultiUnityWeight = { Lb: 100.5, Kg: 45.59 }
      expect(formatWeight(weight2, 'lbs')).toBe('100.5 lbs')

      const weight3: MultiUnityWeight = { Lb: 100.25, Kg: 45.47 }
      expect(formatWeight(weight3, 'lbs')).toBe('100.25 lbs')

      const weight4: MultiUnityWeight = { Lb: 100.999, Kg: 45.45 }
      expect(formatWeight(weight4, 'lbs')).toBe('101 lbs')
    })

    it('should format kilograms with 0.## pattern', () => {
      const weight1: MultiUnityWeight = { Lb: 220.46, Kg: 100 }
      expect(formatWeight(weight1, 'kg')).toBe('100 kg')

      const weight2: MultiUnityWeight = { Lb: 221.56, Kg: 100.5 }
      expect(formatWeight(weight2, 'kg')).toBe('100.5 kg')

      const weight3: MultiUnityWeight = { Lb: 221.12, Kg: 100.25 }
      expect(formatWeight(weight3, 'kg')).toBe('100.25 kg')

      const weight4: MultiUnityWeight = { Lb: 221.78, Kg: 100.599 }
      expect(formatWeight(weight4, 'kg')).toBe('100.6 kg')
    })

    it('should support isKg boolean parameter for backward compatibility', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatWeight(weight, true)).toBe('45.36 kg')
      expect(formatWeight(weight, false)).toBe('100 lbs')
    })
  })

  describe('calculatePercentageChange', () => {
    it('should calculate positive percentage change', () => {
      expect(calculatePercentageChange(110, 100)).toBe(10)
    })

    it('should calculate negative percentage change', () => {
      expect(calculatePercentageChange(90, 100)).toBe(-10)
    })

    it('should handle zero previous value', () => {
      expect(calculatePercentageChange(100, 0)).toBeNull()
    })

    it('should round to 1 decimal place', () => {
      expect(calculatePercentageChange(105, 100)).toBe(5)
      expect(calculatePercentageChange(105.5, 100)).toBe(5.5)
    })
  })

  describe('roundWeight', () => {
    it('should round floating-point precision errors', () => {
      expect(roundWeight(35.00000000000004)).toBe(35)
      expect(roundWeight(45.00000000000001)).toBe(45)
      expect(roundWeight(99.99999999999999)).toBe(100)
    })

    it('should preserve valid decimal values', () => {
      expect(roundWeight(35.5)).toBe(35.5)
      expect(roundWeight(45.25)).toBe(45.25)
      expect(roundWeight(102.75)).toBe(102.75)
    })

    it('should round to 2 decimal places', () => {
      expect(roundWeight(35.123456)).toBe(35.12)
      expect(roundWeight(45.999)).toBe(46)
      expect(roundWeight(102.005)).toBe(102.01)
    })

    it('should handle whole numbers', () => {
      expect(roundWeight(35)).toBe(35)
      expect(roundWeight(100)).toBe(100)
      expect(roundWeight(0)).toBe(0)
    })
  })

  describe('roundToNearestIncrement', () => {
    it('should round to nearest increment', () => {
      expect(roundToNearestIncrement(23, 5)).toBe(25)
      expect(roundToNearestIncrement(22, 5)).toBe(20)
      expect(roundToNearestIncrement(100, 10)).toBe(100)
      expect(roundToNearestIncrement(107, 10)).toBe(110)
    })

    it('should handle increment of 0 by using 1', () => {
      expect(roundToNearestIncrement(23.7, 0)).toBe(24)
      expect(roundToNearestIncrement(22.2, 0)).toBe(22)
    })

    it('should respect minimum value', () => {
      expect(roundToNearestIncrement(5, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(8, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(12, 5, 10)).toBe(10)
      expect(roundToNearestIncrement(15, 5, 10)).toBe(15)
    })

    it('should respect maximum value', () => {
      expect(roundToNearestIncrement(95, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(92, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(88, 5, undefined, 90)).toBe(90)
      expect(roundToNearestIncrement(85, 5, undefined, 90)).toBe(85)
    })

    it('should respect both min and max values', () => {
      expect(roundToNearestIncrement(5, 5, 10, 50)).toBe(10)
      expect(roundToNearestIncrement(52, 5, 10, 50)).toBe(50)
      expect(roundToNearestIncrement(30, 5, 10, 50)).toBe(30)
    })

    it('should handle decimal increments', () => {
      expect(roundToNearestIncrement(22.3, 2.5)).toBe(22.5)
      expect(roundToNearestIncrement(22.1, 2.5)).toBe(22.5)
      expect(roundToNearestIncrement(21.1, 2.5)).toBe(20)
      expect(roundToNearestIncrement(23.8, 2.5)).toBe(25)
    })

    it('should handle small increments', () => {
      expect(roundToNearestIncrement(45.23, 0.25)).toBe(45.25)
      expect(roundToNearestIncrement(45.12, 0.25)).toBe(45)
      expect(roundToNearestIncrement(45.37, 0.25)).toBe(45.25)
      expect(roundToNearestIncrement(45.38, 0.25)).toBe(45.5)
    })
  })

  describe('truncateDecimal', () => {
    it('should truncate to specified precision', () => {
      expect(truncateDecimal(3.14159, 2)).toBe(3.14)
      expect(truncateDecimal(3.14159, 3)).toBe(3.141)
      expect(truncateDecimal(3.14159, 4)).toBe(3.1415)
    })

    it('should handle precision of 0', () => {
      expect(truncateDecimal(3.14159, 0)).toBe(3)
      expect(truncateDecimal(3.99999, 0)).toBe(3)
      expect(truncateDecimal(4.00001, 0)).toBe(4)
    })

    it('should handle negative numbers', () => {
      expect(truncateDecimal(-3.14159, 2)).toBe(-3.14)
      expect(truncateDecimal(-3.99999, 1)).toBe(-3.9)
      expect(truncateDecimal(-4.00001, 0)).toBe(-4)
    })

    it('should not round up', () => {
      expect(truncateDecimal(3.999, 2)).toBe(3.99)
      expect(truncateDecimal(3.999, 1)).toBe(3.9)
      expect(truncateDecimal(3.999, 0)).toBe(3)
    })

    it('should handle integers', () => {
      expect(truncateDecimal(42, 2)).toBe(42)
      expect(truncateDecimal(100, 0)).toBe(100)
    })

    it('should handle very small numbers', () => {
      expect(truncateDecimal(0.00123456, 4)).toBe(0.0012)
      expect(truncateDecimal(0.00123456, 5)).toBe(0.00123)
    })
  })

  describe('formatWeightForDisplay', () => {
    it('should format whole numbers without decimals', () => {
      expect(formatWeightForDisplay(100)).toBe('100')
      expect(formatWeightForDisplay(135)).toBe('135')
      expect(formatWeightForDisplay(0)).toBe('0')
    })

    it('should format meaningful decimals up to 2 places', () => {
      expect(formatWeightForDisplay(137.5)).toBe('137.5')
      expect(formatWeightForDisplay(102.25)).toBe('102.25')
      expect(formatWeightForDisplay(45.75)).toBe('45.75')
    })

    it('should remove excessive decimals from floating point errors', () => {
      expect(formatWeightForDisplay(135.12345678)).toBe('135.12')
      expect(formatWeightForDisplay(100.00000001)).toBe('100')
      expect(formatWeightForDisplay(99.99999999)).toBe('100')
    })

    it('should round to nearest increment when provided', () => {
      // 2.5 lb increment
      expect(formatWeightForDisplay(136, 2.5)).toBe('135')
      expect(formatWeightForDisplay(136.25, 2.5)).toBe('137.5')
      expect(formatWeightForDisplay(138.7, 2.5)).toBe('137.5')

      // 1 kg increment
      expect(formatWeightForDisplay(43.3, 1)).toBe('43')
      expect(formatWeightForDisplay(43.7, 1)).toBe('44')
      expect(formatWeightForDisplay(43.5, 1)).toBe('44')

      // 5 lb increment
      expect(formatWeightForDisplay(132, 5)).toBe('130')
      expect(formatWeightForDisplay(133, 5)).toBe('135')
      expect(formatWeightForDisplay(137.5, 5)).toBe('140')
    })

    it('should handle edge cases', () => {
      expect(formatWeightForDisplay(null as any)).toBe('0')
      expect(formatWeightForDisplay(undefined as any)).toBe('0')
      expect(formatWeightForDisplay(NaN)).toBe('0')
      expect(formatWeightForDisplay(-5)).toBe('0')
    })

    it('should format very small weights correctly', () => {
      expect(formatWeightForDisplay(0.5)).toBe('0.5')
      expect(formatWeightForDisplay(0.25)).toBe('0.25')
      expect(formatWeightForDisplay(0.123)).toBe('0.12')
    })

    it('should handle default increments correctly', () => {
      // When no increment provided, just format with proper decimals
      expect(formatWeightForDisplay(135.123456)).toBe('135.12')
      expect(formatWeightForDisplay(100.5)).toBe('100.5')
      expect(formatWeightForDisplay(102)).toBe('102')
    })

    it('should not show unnecessary trailing zeros', () => {
      expect(formatWeightForDisplay(100.0)).toBe('100')
      expect(formatWeightForDisplay(100.5)).toBe('100.5')
      expect(formatWeightForDisplay(100.1)).toBe('100.1')
    })
  })

  describe('formatRecommendationWeight', () => {
    it('should format weight with increment rounding', () => {
      const weight: MultiUnityWeight = { Lb: 102.7, Kg: 46.58372 }
      expect(formatRecommendationWeight(weight, 'lbs', 5)).toBe('105 lbs')
    })

    it('should format kg weight with increment rounding', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.3598721 }
      expect(formatRecommendationWeight(weight, 'kg', 2.5)).toBe('45 kg')
    })

    it('should handle small increments correctly', () => {
      const weight: MultiUnityWeight = { Lb: 137.3, Kg: 62.3 }
      expect(formatRecommendationWeight(weight, 'lbs', 2.5)).toBe('137.5 lbs')
    })

    it('should handle no increment (null/undefined)', () => {
      const weight: MultiUnityWeight = { Lb: 102.7654321, Kg: 46.58372 }
      expect(formatRecommendationWeight(weight, 'lbs', null)).toBe('102.77 lbs')
      expect(formatRecommendationWeight(weight, 'lbs', undefined)).toBe(
        '102.77 lbs'
      )
    })

    it('should handle zero increment', () => {
      const weight: MultiUnityWeight = { Lb: 102.7654321, Kg: 46.58372 }
      expect(formatRecommendationWeight(weight, 'lbs', 0)).toBe('102.77 lbs')
    })

    it('should handle bodyweight exercises (zero weight)', () => {
      const weight: MultiUnityWeight = { Lb: 0, Kg: 0 }
      expect(formatRecommendationWeight(weight, 'lbs', 5)).toBe('0 lbs')
    })

    it('should handle null weight gracefully', () => {
      expect(formatRecommendationWeight(null, 'lbs', 5)).toBe('0 lbs')
    })

    it('should format with proper decimal places when no rounding needed', () => {
      const weight: MultiUnityWeight = { Lb: 100, Kg: 45.36 }
      expect(formatRecommendationWeight(weight, 'lbs', 5)).toBe('100 lbs')
    })

    it('should handle very large increments', () => {
      const weight: MultiUnityWeight = { Lb: 123, Kg: 55.79 }
      expect(formatRecommendationWeight(weight, 'lbs', 20)).toBe('120 lbs')
    })

    it('should handle fractional increments', () => {
      const weight: MultiUnityWeight = { Lb: 45.7, Kg: 20.73 }
      expect(formatRecommendationWeight(weight, 'lbs', 0.5)).toBe('45.5 lbs')
    })
  })
})
