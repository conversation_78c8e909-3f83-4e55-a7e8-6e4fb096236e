name: Optimized CI Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop, staging]

# Cancel outdated runs when new commits are pushed
# Protects main, develop, and staging branch merges from being cancelled
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' && github.ref != 'refs/heads/develop' && github.ref != 'refs/heads/staging' }}

env:
  NODE_VERSION: '20.x'
  # Memory and resource management for WebKit stability
  NODE_OPTIONS: '--max_old_space_size=8192 --max-semi-space-size=512'
  # WebKit-specific environment variables
  WEBKIT_DISABLE_COMPOSITING: '1'
  WEBKIT_FORCE_COMPOSITING_MODE: '0'
  # Playwright stability settings - don't set PLAYWRIGHT_BROWSERS_PATH globally
  # as it interferes with browser installation
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '0'

jobs:
  # Quick validation checks - run everything in parallel
  quick-checks:
    name: Quick Validation
    runs-on: [self-hosted, macos]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Validate critical tests (prevent placeholder regression)
        run: node scripts/validate-critical-tests.js
      
      - name: Run all quick checks in parallel
        run: |
          npm run typecheck &
          npm run lint &
          npm run prettier:check &
          npm run check:file-sizes &
          wait

  # Unit tests with coverage
  test-unit:
    name: Unit Tests
    runs-on: [self-hosted, macos]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for --changed flag to work properly
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run affected tests first
        id: affected_tests
        continue-on-error: true
        run: |
          echo "Running tests for changed files only..."
          # Set memory limits and test environment
          export NODE_OPTIONS="--max_old_space_size=8192 --max-semi-space-size=512"
          export NODE_ENV=test
          export NEXT_PUBLIC_DISABLE_OAUTH=true
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            # For PRs, compare with the base branch
            npm run test:changed -- --reporter=verbose
          else
            # For direct pushes, compare with the previous commit
            npm run test:related -- --reporter=verbose
          fi
      
      - name: Run full test suite if affected tests failed or on main/staging branch
        if: steps.affected_tests.outcome == 'failure' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
        run: |
          # Set memory limits and test environment for coverage test execution
          export NODE_OPTIONS="--max_old_space_size=10240 --max-semi-space-size=1024"
          export NODE_ENV=test
          export NEXT_PUBLIC_DISABLE_OAUTH=true
          npm run test:coverage
      
      - name: Upload coverage
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/
          retention-days: 7

  # Build and analyze
  build:
    name: Build & Analyze
    runs-on: [self-hosted, macos]
    needs: [quick-checks]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: |
          echo "Starting build process..."
          echo "Node version: $(node --version)"
          echo "NPM version: $(npm --version)"
          echo "Current directory: $(pwd)"
          echo "Available disk space:"
          df -h .
          echo "Starting Next.js build..."
          npm run build

      - name: Assert build output exists
        run: |
          if [ ! -d ".next" ]; then
            echo "::error::.next directory not found after build"
            echo "Build failed to generate output directory"
            echo "Current directory contents:"
            ls -la
            echo "Checking for any build-related directories:"
            find . -maxdepth 2 -type d -name "*build*" -o -name "*dist*" -o -name "*out*" 2>/dev/null || true
            echo "Checking npm cache and temp directories:"
            npm cache verify || true
            echo "Node modules status:"
            ls -la node_modules/.bin/next 2>/dev/null || echo "Next.js binary not found"
            exit 1
          fi
          echo "✅ .next directory found"
          echo "Build output size:"
          du -sh .next/
          echo "Build output structure:"
          find .next -type f -name "*.js" -o -name "*.json" | head -10

      - name: Debug - Check build output
        run: |
          echo "Current directory: $(pwd)"
          echo "Checking for .next directory..."
          if [ -d ".next" ]; then
            echo "✅ .next directory found"
            echo "Contents:"
            ls -la .next/
          else
            echo "❌ .next directory NOT found"
            echo "Checking current directory contents:"
            ls -la
            echo "Checking for any next-related directories:"
            find . -type d -name "*next*" -not -path "./node_modules/*" 2>/dev/null || true
          fi
      
      - name: Check bundle size
        run: |
          npm run analyze > bundle-analysis.txt 2>&1 || true
          if grep -q "First Load JS" bundle-analysis.txt; then
            # Extract the number before 'kB' in the First Load JS line
            BUNDLE_SIZE=$(grep "First Load JS" bundle-analysis.txt | grep -oE '[0-9]+(\.[0-9]+)? kB' | head -1 | grep -oE '[0-9]+(\.[0-9]+)?')
            if [ -n "$BUNDLE_SIZE" ]; then
              echo "Bundle size: ${BUNDLE_SIZE}KB"
              if (( $(echo "$BUNDLE_SIZE > 150" | bc -l) )); then
                echo "⚠️ Bundle size exceeds 150KB limit!"
              fi
            else
              echo "⚠️ Could not parse bundle size from output"
              cat bundle-analysis.txt
            fi
          else
            echo "⚠️ 'First Load JS' not found in bundle analysis"
            cat bundle-analysis.txt
          fi
      
      - name: Upload build
        uses: actions/upload-artifact@v4
        with:
          name: build-output
          path: .next/
          retention-days: 1
          if-no-files-found: error  # Changed from 'warn' to 'error' to fail fast
          compression-level: 0

  # Critical E2E tests - must pass quickly
  e2e-critical:
    name: Critical E2E Tests
    runs-on: [self-hosted, macos]
    timeout-minutes: 30  # Reduce timeout to prevent hanging
    needs: [quick-checks]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_APP_ENV: test

      - name: Assert build output exists
        run: |
          if [ ! -d ".next" ]; then
            echo "::error::.next directory not found after build"
            echo "Build failed to generate output directory"
            exit 1
          fi
          echo "✅ .next directory found for E2E tests"
      
      - name: Prepare system for WebKit
        run: |
          echo "Preparing macOS system for WebKit..."
          # Ensure system is ready for WebKit
          sudo xcode-select --install || true
          # Clear any existing browser caches that might interfere
          rm -rf ~/Library/Caches/com.apple.Safari* || true
          rm -rf ~/Library/Caches/WebKit* || true
          # Set system limits for better stability
          ulimit -n 4096 || true
          echo "System preparation completed"

      - name: Setup Playwright browsers
        uses: ./.github/actions/setup-playwright
        with:
          browsers: 'webkit chromium'

      - name: Pre-test WebKit validation
        run: |
          echo "🔧 Validating WebKit installation before tests..."
          unset PLAYWRIGHT_BROWSERS_PATH

          # Check if WebKit browser directory exists
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            CACHE_PATH="$HOME/Library/Caches/ms-playwright"
          else
            CACHE_PATH="$HOME/.cache/ms-playwright"
          fi

          echo "Checking cache path: $CACHE_PATH"
          if [ -d "$CACHE_PATH" ]; then
            echo "Cache directory exists, listing contents:"
            ls -la "$CACHE_PATH"/ || echo "Cannot list cache directory"
          else
            echo "❌ Cache directory does not exist: $CACHE_PATH"
            exit 1
          fi

          if ls "$CACHE_PATH"/webkit-* 1> /dev/null 2>&1; then
            echo "✅ WebKit browser directory found"
            WEBKIT_DIR=$(ls -d "$CACHE_PATH"/webkit-* | head -1)
            echo "WebKit directory: $WEBKIT_DIR"
            echo "WebKit directory contents:"
            ls -la "$WEBKIT_DIR" | head -10

            # Check for executable files
            echo "Checking for WebKit executable files:"
            find "$WEBKIT_DIR" -name "*WebKit*" -type f -executable 2>/dev/null | head -5 || echo "No executable files found"
          else
            echo "❌ WebKit browser directory not found"
            echo "Available browsers:"
            ls -la "$CACHE_PATH"/ || echo "Cache directory not accessible"
            exit 1
          fi

          # Test basic Playwright functionality
          echo "Testing Playwright version and browser list:"
          npx playwright --version
          npx playwright install --dry-run webkit || echo "Dry run failed"
          echo "✅ Pre-test validation completed"

      - name: Run critical flow tests
        run: |
          # Ensure Playwright can locate its browsers
          unset PLAYWRIGHT_BROWSERS_PATH

          # Set WebKit-specific environment variables for stability
          export WEBKIT_DISABLE_COMPOSITING=1
          export WEBKIT_FORCE_COMPOSITING_MODE=0

          # Verify browser installation before running tests
          echo "Verifying browser installation..."
          npx playwright --version

          # Test WebKit launch capability before running tests
          echo "Testing WebKit launch capability..."
          timeout 30s npx playwright test --config=playwright.ci.optimized.config.ts --list --project="Mobile Safari Critical" || echo "WebKit test failed but continuing..."

          # Run tests with enhanced error handling
          npm run test:critical-flows -- --retries=2
        env:
          CI: true
          PLAYWRIGHT_RETRIES: 2
          PLAYWRIGHT_BROWSERS_PATH: ''
          # WebKit stability environment variables
          WEBKIT_DISABLE_COMPOSITING: '1'
          WEBKIT_FORCE_COMPOSITING_MODE: '0'
      
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-critical
          path: playwright-report/
          retention-days: 7

  # Full E2E suite - only on main/staging or when needed
  e2e-full:
    name: Full E2E Suite
    runs-on: [self-hosted, macos]
    timeout-minutes: 45  # Reduce timeout to prevent hanging
    needs: [quick-checks]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging' || contains(github.event.pull_request.labels.*.name, 'full-test')
    
    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_APP_ENV: test

      - name: Assert build output exists
        run: |
          if [ ! -d ".next" ]; then
            echo "::error::.next directory not found after build"
            echo "Build failed to generate output directory"
            exit 1
          fi
          echo "✅ .next directory found for full E2E tests"

      - name: Cleanup zombie browser processes
        run: |
          echo "Cleaning up zombie processes..."
          pkill -9 chromium || true
          pkill -9 chrome || true
          pkill -9 firefox || true
          pkill -9 webkit || true
          pkill -9 Safari || true
          pkill -9 WebKit || true
          pkill -f "Playwright" || true
          echo "Process cleanup completed"

      - name: Check system resources
        uses: ./.github/actions/check-system-resources
        with:
          min-disk-gb: 2
          min-memory-gb: 1
          cleanup-on-low-resources: true

      - name: Prepare system for WebKit
        run: |
          echo "Preparing macOS system for WebKit..."
          # Ensure system is ready for WebKit
          sudo xcode-select --install || true
          # Clear any existing browser caches that might interfere
          rm -rf ~/Library/Caches/com.apple.Safari* || true
          rm -rf ~/Library/Caches/WebKit* || true
          # Set system limits for better stability
          ulimit -n 4096 || true
          echo "System preparation completed"

      - name: Setup Playwright browsers
        uses: ./.github/actions/setup-playwright
        with:
          browsers: 'webkit chromium'

      - name: Pre-test WebKit validation (Full E2E)
        run: |
          echo "🔧 Validating WebKit installation for full E2E tests..."
          unset PLAYWRIGHT_BROWSERS_PATH

          # Check if WebKit browser directory exists
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            CACHE_PATH="$HOME/Library/Caches/ms-playwright"
          else
            CACHE_PATH="$HOME/.cache/ms-playwright"
          fi

          if ls "$CACHE_PATH"/webkit-* 1> /dev/null 2>&1; then
            echo "✅ WebKit browser directory found"
            WEBKIT_DIR=$(ls -d "$CACHE_PATH"/webkit-* | head -1)
            echo "WebKit directory: $WEBKIT_DIR"
          else
            echo "❌ WebKit browser directory not found - this will cause test failures"
            echo "Available browsers:"
            ls -la "$CACHE_PATH"/ || echo "Cache directory not accessible"
          fi

      - name: Print system resources
        run: |
          echo "=== System Resource Report ==="
          echo "Free disk space:"
          df -h
          echo ""
          echo "Memory usage:"
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            vm_stat | head -10
          else
            free -h
          fi
          echo ""
          echo "CPU usage:"
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            top -l 1 -n 20
          else
            top -bn1 | head -20
          fi
          echo ""
          echo "Process count:"
          ps aux | wc -l
          echo ""
          echo "Node/npm versions:"
          node --version
          npm --version
          echo "=== End Resource Report ==="

      - name: Run full tests (shard ${{ matrix.shard }}/4)
        run: |
          # Ensure PLAYWRIGHT_BROWSERS_PATH is not set to interfere with browser detection
          unset PLAYWRIGHT_BROWSERS_PATH

          # Set WebKit-specific environment variables for stability
          export WEBKIT_DISABLE_COMPOSITING=1
          export WEBKIT_FORCE_COMPOSITING_MODE=0

          # Verify browser installation before running tests
          echo "Verifying browser installation..."
          npx playwright --version

          # Test WebKit launch capability before running tests
          echo "Testing WebKit launch capability..."
          timeout 30s npx playwright test --config=playwright.ci.optimized.config.ts --list --project="Mobile Safari Full" || echo "WebKit test failed but continuing..."

          # Run the tests with enhanced error handling
          npx playwright test --config=playwright.ci.optimized.config.ts --shard=${{ matrix.shard }}/4
        env:
          CI: true
          # Ensure browser path is not set to avoid conflicts
          PLAYWRIGHT_BROWSERS_PATH: ''
          # WebKit stability environment variables
          WEBKIT_DISABLE_COMPOSITING: '1'
          WEBKIT_FORCE_COMPOSITING_MODE: '0'

      - name: Cleanup browser processes after tests
        if: always()
        run: |
          echo "Post-test browser cleanup..."
          pkill -9 chromium || true
          pkill -9 chrome || true
          pkill -9 firefox || true
          pkill -9 webkit || true
          pkill -9 playwright || true
          echo "Post-test cleanup completed"

      - name: Print system resources on failure
        if: failure()
        run: |
          echo "=== POST-FAILURE Resource Report ==="
          echo "Free disk space:"
          df -h
          echo ""
          echo "Memory usage:"
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            vm_stat | head -10
          else
            free -h
          fi
          echo ""
          echo "CPU usage:"
          if [[ "$RUNNER_OS" == "macOS" ]]; then
            top -l 1 -n 20
          else
            top -bn1 | head -20
          fi
          echo ""
          echo "Running processes:"
          ps aux | grep -E "(playwright|chromium|webkit|firefox)" || true
          echo ""
          echo "Zombie processes:"
          ps aux | grep defunct || true
          echo "=== End POST-FAILURE Report ==="
      
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-full-${{ matrix.shard }}
          path: |
            playwright-report/
            test-results/
          retention-days: 7

  # Security and performance checks - run in parallel
  quality-gates:
    name: Quality Gates
    runs-on: [self-hosted, macos]
    needs: [quick-checks]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Security audit
        run: |
          echo "Running security audit..."
          npm audit --audit-level=high
          echo "✅ Security audit passed"

      - name: License check
        run: |
          echo "Checking licenses..."
          npm run audit:licenses
          echo "✅ License check passed"

      - name: API contract tests
        run: |
          echo "Running API contract tests..."
          npm run test:api:contracts
          echo "✅ API contract tests passed"

  # Coverage comment on PRs
  coverage-comment:
    name: Coverage Report Comment
    runs-on: [self-hosted, macos]
    needs: [test-unit]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download coverage report
        uses: actions/download-artifact@v4
        with:
          name: coverage-report
          path: coverage/
      
      - name: Comment coverage on PR
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            
            try {
              const coverageJson = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
              const total = coverageJson.total;
              
              const coverageTable = `
              | Type | Coverage | Covered | Total |
              |------|----------|---------|-------|
              | Lines | ${total.lines.pct}% | ${total.lines.covered} | ${total.lines.total} |
              | Statements | ${total.statements.pct}% | ${total.statements.covered} | ${total.statements.total} |
              | Functions | ${total.functions.pct}% | ${total.functions.covered} | ${total.functions.total} |
              | Branches | ${total.branches.pct}% | ${total.branches.covered} | ${total.branches.total} |
              `;
              
              const avgCoverage = (total.lines.pct + total.statements.pct + total.functions.pct + total.branches.pct) / 4;
              const meetsThreshold = avgCoverage >= 80;
              
              const comment = `## 📊 Coverage Report
              
              ${meetsThreshold ? '✅ Coverage meets the 80% threshold!' : '⚠️ **Warning:** Coverage is below the 80% threshold!'}
              
              ${coverageTable}
              
              **Average Coverage:** ${avgCoverage.toFixed(2)}%`;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: comment
              });
            } catch (error) {
              console.error('Failed to parse coverage report:', error);
            }

  # Lighthouse performance check - only on PRs
  performance:
    name: Performance Check
    runs-on: [self-hosted, macos]
    needs: [build]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Download build
        uses: actions/download-artifact@v4
        with:
          name: build-output
          path: .next/
      
      - name: Run Lighthouse
        run: npm run lighthouse:mobile || true
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Final status check
  ci-status:
    name: CI Status
    runs-on: [self-hosted, macos]
    needs: [quick-checks, test-unit, e2e-critical, quality-gates]
    if: always()
    
    steps:
      - name: Check required jobs
        run: |
          if [[ "${{ needs.quick-checks.result }}" == "success" &&
                "${{ needs.test-unit.result }}" == "success" &&
                "${{ needs.e2e-critical.result }}" == "success" &&
                "${{ needs.quality-gates.result }}" == "success" ]]; then
            echo "✅ All required checks passed!"
            exit 0
          else
            echo "❌ Some required checks failed!"
            echo "Quick checks: ${{ needs.quick-checks.result }}"
            echo "Unit tests: ${{ needs.test-unit.result }}"
            echo "Critical E2E: ${{ needs.e2e-critical.result }}"
            echo "Quality gates: ${{ needs.quality-gates.result }}"
            exit 1
          fi
