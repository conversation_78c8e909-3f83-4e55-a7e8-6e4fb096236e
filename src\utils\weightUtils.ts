/**
 * Weight formatting and calculation utilities
 */

import type { MultiUnityWeight } from '@/types'

/**
 * Format weight value with unit
 * @param weight - Weight object with Lb and Kg values
 * @param unitOrIsKg - Unit to display ('lbs' or 'kg') or boolean for backward compatibility
 * @returns Formatted weight string
 */
export function formatWeight(
  weight: MultiUnityWeight,
  unitOrIsKg: 'lbs' | 'kg' | boolean = 'lbs'
): string {
  if (!weight) return '0 lbs'

  // Handle backward compatibility with boolean parameter
  let unit: 'lbs' | 'kg'
  if (typeof unitOrIsKg === 'boolean') {
    unit = unitOrIsKg ? 'kg' : 'lbs'
  } else {
    unit = unitOrIsKg
  }

  const value = unit === 'lbs' ? weight.Lb : weight.Kg

  // Format with 0.## pattern (mobile format)
  // Round to 2 decimal places, then format to remove trailing zeros
  const rounded = Math.round(value * 100) / 100
  const formattedValue = rounded.toFixed(2).replace(/\.?0+$/, '')

  return `${formattedValue} ${unit}`
}

/**
 * Calculate percentage change between two values
 * @param current - Current value
 * @param previous - Previous value
 * @returns Percentage change or null if previous is 0
 */
export function calculatePercentageChange(
  current: number,
  previous: number
): number | null {
  if (previous === 0) return null

  const change = ((current - previous) / previous) * 100
  return Math.round(change * 10) / 10 // Round to 1 decimal place
}

/**
 * Round weight to a reasonable precision to avoid floating-point display issues
 * @param weight - Weight value to round
 * @returns Rounded weight value
 */
export function roundWeight(weight: number): number {
  // Round to 2 decimal places to handle floating-point precision errors
  // while maintaining reasonable precision for fractional weights
  return Math.round(weight * 100) / 100
}

/**
 * Round weight to the nearest increment (e.g., plate sizes)
 * @param weight - Weight value to round
 * @param increment - Increment to round to (e.g., 2.5, 5, 10)
 * @param min - Optional minimum value
 * @param max - Optional maximum value
 * @returns Rounded weight value
 */
export function roundToNearestIncrement(
  weight: number,
  increment: number,
  min?: number,
  max?: number
): number {
  // Handle zero increment by defaulting to 1
  if (increment === 0) {
    increment = 1
  }

  // Round to nearest increment
  let rounded = Math.round(weight / increment) * increment

  // Apply min/max constraints
  if (min !== undefined && rounded < min) {
    rounded = min
  }
  if (max !== undefined && rounded > max) {
    rounded = max
  }

  return rounded
}

/**
 * Truncate decimal to specified precision without rounding
 * @param value - Number to truncate
 * @param precision - Number of decimal places to keep
 * @returns Truncated number
 */
export function truncateDecimal(value: number, precision: number): number {
  const step = Math.pow(10, precision)
  const tmp = Math.trunc(step * value)
  return tmp / step
}

/**
 * Format weight for display with proper decimal handling
 * @param weight - Weight value to format
 * @param increment - Optional increment to round to (e.g., 2.5 lbs, 1 kg)
 * @returns Formatted weight string with appropriate decimals
 */
export function formatWeightForDisplay(
  weight: number | null | undefined,
  increment?: number
): string {
  // Handle edge cases
  if (weight == null || Number.isNaN(weight) || weight < 0) {
    return '0'
  }

  let formattedWeight = weight

  // If increment is provided, round to nearest increment
  if (increment && increment > 0) {
    formattedWeight = roundToNearestIncrement(weight, increment)
  } else {
    // Otherwise, just round to handle floating point errors
    formattedWeight = roundWeight(weight)
  }

  // Format the number to remove unnecessary decimals
  // First round to 2 decimal places
  const rounded = Math.round(formattedWeight * 100) / 100

  // Convert to string and remove trailing zeros
  let result = rounded.toFixed(2)

  // Remove trailing zeros after decimal point
  result = result.replace(/\.?0+$/, '')

  return result
}

/**
 * Format weight for recommendation display with increment-based rounding
 * @param weight - Weight object with Lb and Kg values
 * @param unit - Unit to display ('lbs' or 'kg')
 * @param increment - Weight increment to round to (e.g., 2.5, 5, 10)
 * @returns Formatted weight string with appropriate rounding
 */
export function formatRecommendationWeight(
  weight: MultiUnityWeight | null,
  unit: 'lbs' | 'kg' = 'lbs',
  increment?: number | null
): string {
  if (!weight) return `0 ${unit}`

  const value = unit === 'lbs' ? weight.Lb : weight.Kg

  // If no increment or increment is 0, use standard formatting
  if (!increment || increment === 0) {
    const rounded = Math.round(value * 100) / 100
    return `${rounded} ${unit}`
  }

  // Round to nearest increment
  const rounded = roundToNearestIncrement(value, increment)

  // Format the rounded value, removing unnecessary decimals
  const formatted = rounded.toFixed(2).replace(/\.?0+$/, '')

  return `${formatted} ${unit}`
}
