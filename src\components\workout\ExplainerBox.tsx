'use client'

import React from 'react'
import type { RecommendationModel } from '@/types'
import { useSetMetrics } from '@/hooks/useSetMetrics'
import { formatWeightForDisplay } from '@/utils/weightUtils'

interface ExplainerBoxProps {
  recommendation: RecommendationModel | null
  currentSetIndex: number
  isWarmup: boolean
  unit: 'kg' | 'lbs'
  currentReps?: number
  currentWeight?: number
  isFirstWorkSet?: boolean
}

export function ExplainerBox({
  recommendation,
  currentSetIndex,
  isWarmup,
  unit,
  currentReps,
  currentWeight,
  isFirstWorkSet = false,
}: ExplainerBoxProps) {
  // Use the shared useSetMetrics hook (must be called before any early returns)
  const { lastTimeReps, lastTimeWeight, oneRMProgress } = useSetMetrics({
    recommendation,
    currentSetIndex,
    isWarmup,
    unit,
    isFirstWorkSet,
    currentReps,
    currentWeight,
  })

  if (!recommendation) return null

  const warmupCount = recommendation.WarmupsCount || 0
  const workSetCount = recommendation.Series || 0

  return (
    <div className="px-4 py-3 bg-bg-secondary/50">
      <div className="text-center space-y-1">
        {/* Set structure info */}
        <p className="text-sm text-text-secondary italic">
          {warmupCount} warm-up{warmupCount !== 1 ? 's' : ''}, {workSetCount}{' '}
          work set{workSetCount !== 1 ? 's' : ''}
        </p>

        {/* Last time values */}
        {lastTimeReps !== null && lastTimeWeight !== null && (
          <p className="text-sm text-text-secondary italic">
            Last time: {lastTimeReps} ×{' '}
            {formatWeightForDisplay(
              lastTimeWeight,
              unit === 'lbs'
                ? recommendation.Increments?.Lb
                : recommendation.Increments?.Kg
            )}{' '}
            {unit}
          </p>
        )}

        {/* 1RM Progress */}
        {oneRMProgress !== null && (
          <p className="text-sm font-medium text-brand-primary">
            1RM Progress: {oneRMProgress > 0 ? '+' : ''}
            {oneRMProgress.toFixed(1)}%
          </p>
        )}
      </div>
    </div>
  )
}
