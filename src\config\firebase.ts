/**
 * Firebase Configuration for Dr. Muscle X
 *
 * This configuration is shared with the mobile app to ensure consistency
 * across platforms. The Firebase project "drmuscle-4a08d" is used for
 * authentication services.
 */

import { initializeApp, FirebaseApp, FirebaseOptions } from 'firebase/app'
import { getAuth, Auth } from 'firebase/auth'

/**
 * Firebase configuration object
 * These values are from the Firebase project used by the mobile app
 */
const firebaseConfig: FirebaseOptions = {
  apiKey: 'AIzaSyA_mW49NWFRom2vdZO5qimdWhTzP2HuaQE',
  authDomain: 'drmuscle-4a08d.firebaseapp.com',
  projectId: 'drmuscle-4a08d',
  storageBucket: 'drmuscle-4a08d.appspot.com',
  messagingSenderId: '707210235326',
  appId: '1:707210235326:web:7c4a0e8f4e8b9c3d',
  // Note: measurementId is optional and not included in mobile config
}

/**
 * Initialize Firebase app instance
 * This is done once and reused throughout the application
 */
let app: FirebaseApp | undefined
let auth: Auth | undefined

/**
 * Get or initialize Firebase app instance
 */
export function getFirebaseApp(): FirebaseApp {
  if (!app) {
    app = initializeApp(firebaseConfig)
  }
  return app
}

/**
 * Get or initialize Firebase Auth instance
 */
export function getFirebaseAuth(): Auth {
  if (!auth) {
    auth = getAuth(getFirebaseApp())
  }
  return auth
}

/**
 * Check if Firebase is initialized
 */
export function isFirebaseInitialized(): boolean {
  return !!app && !!auth
}

/**
 * Firebase OAuth configuration
 * These are the OAuth client IDs used by the mobile app
 */
export const firebaseOAuthConfig = {
  // Web client ID for Google Sign-In
  googleClientId:
    process.env.NODE_ENV === 'test'
      ? 'test-google-client-id'
      : '707210235326-204je3om2b9im1irln4g1ib90uocr9gc.apps.googleusercontent.com',
  // iOS client ID (for reference, not used in web)
  googleClientIdIOS:
    '707210235326-ldcslmjtnjib5bklf23efrhp8u9qrpq3.apps.googleusercontent.com',
  // Android client ID (for reference, not used in web)
  googleClientIdAndroid:
    '707210235326-jaecneoq7ckco5j2l85j94i016c0g7j5.apps.googleusercontent.com',
  // Apple configuration
  apple: {
    teamId: process.env.NODE_ENV === 'test' ? 'test-team-id' : '7AAXZ47995',
    bundleId:
      process.env.NODE_ENV === 'test'
        ? 'test-bundle-id'
        : 'com.drmaxmuscle.max',
  },
} as const

const firebaseHelpers = {
  getFirebaseApp,
  getFirebaseAuth,
  isFirebaseInitialized,
  firebaseOAuthConfig,
}

export default firebaseHelpers
