'use client'

import { useState, useEffect } from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { ExerciseTimer } from './ExerciseTimer'
import { ExerciseSwapModal } from './ExerciseSwapModal'
import { WorkoutSuccessScreen } from './WorkoutSuccessScreen'
import { clearBadWeightData } from '@/utils/clearBadWeightData'
import { logger } from '@/utils/logger'
import { formatRecommendationWeight } from '@/utils/weightUtils'
import type { RecommendationModel } from '@/types'

export function WorkoutScreen() {
  const {
    todaysWorkout,
    currentWorkout,
    currentExercise,
    currentSetIndex,
    workoutSession,
    isLoadingWorkout,
    isLoading,
    workoutError,
    error,
    startWorkout,
    finishWorkout,
    goToNextExercise,
    getRecommendation,
    loadAllExerciseRecommendations,
    isOffline,
  } = useWorkout()

  const [isSwapModalOpen, setIsSwapModalOpen] = useState(false)
  const [recommendation, setRecommendation] =
    useState<RecommendationModel | null>(null)
  const [savedOfflineMessage] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [hasLoadedAllRecommendations, setHasLoadedAllRecommendations] =
    useState(false)

  // Load all exercise recommendations when workout page loads
  useEffect(() => {
    if (currentWorkout && !hasLoadedAllRecommendations) {
      loadAllExerciseRecommendations()
      setHasLoadedAllRecommendations(true)
    }
  }, [
    currentWorkout,
    loadAllExerciseRecommendations,
    hasLoadedAllRecommendations,
  ])

  // Clear bad weight data on component mount (one-time fix)
  useEffect(() => {
    clearBadWeightData()
  }, [])

  // Fetch recommendation when exercise changes
  useEffect(() => {
    if (currentExercise) {
      getRecommendation(currentExercise.Id).then(setRecommendation)
    }
  }, [currentExercise, getRecommendation])

  // Handle loading state
  if (isLoadingWorkout) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4 bg-bg-primary">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-brand-primary border-t-transparent" />
          <p className="text-text-secondary">Loading workout...</p>
        </div>
      </div>
    )
  }

  // Handle error state
  if (workoutError || error) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4 bg-bg-primary">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-error">
            Error loading workout
          </h2>
          <p className="text-text-secondary">
            {workoutError instanceof Error
              ? workoutError.message
              : error || 'Failed to load'}
          </p>
        </div>
      </div>
    )
  }

  // Handle no workout state
  if (!todaysWorkout || todaysWorkout.length === 0 || !currentWorkout) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4 bg-bg-primary">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-text-primary">
            No workout scheduled
          </h2>
          <p className="text-text-secondary">Check back tomorrow!</p>
        </div>
      </div>
    )
  }

  // Calculate exercise progress
  const totalExercises = currentWorkout.Exercises?.length || 0
  const currentExerciseNumber =
    currentWorkout?.Exercises?.findIndex(
      (ex) => ex.Id === currentExercise?.Id
    ) ?? -1
  const adjustedExerciseNumber = currentExerciseNumber + 1

  const handleStartWorkout = () => {
    if (todaysWorkout) {
      startWorkout(todaysWorkout)
    }
  }

  const handleSkipExercise = () => {
    goToNextExercise()
  }

  const handleSwapExercise = () => {
    setIsSwapModalOpen(true)
  }

  const handleSwap = async (result: {
    sourceExercise: { Label: string }
    targetExercise: { Label: string; Id: number }
    savedToDb: boolean
  }) => {
    try {
      setIsSwapModalOpen(false)

      if (result.savedToDb) {
        // For permanent swaps, reload the page to get updated workout data
        logger.log(
          `[WorkoutScreen] Permanent swap saved: ${result.sourceExercise.Label} -> ${result.targetExercise.Label}`
        )
        window.location.reload()
      } else {
        // For temporary swaps, update local state
        logger.log(
          `[WorkoutScreen] Temporary swap applied: ${result.sourceExercise.Label} -> ${result.targetExercise.Label}`
        )

        // Clear current recommendation since we have a new exercise
        setRecommendation(null)

        // Load recommendation for the new exercise
        getRecommendation(result.targetExercise.Id).then(setRecommendation)
      }
    } catch (error) {
      logger.error('[WorkoutScreen] Failed to handle swap result:', error)
    }
  }

  const handleFinishWorkout = async () => {
    try {
      await finishWorkout()
      setShowSuccess(true)
    } catch (error) {
      // Handle error silently
    }
  }

  const hasCompletedSets = workoutSession?.exercises.some(
    (ex) => ex.sets.length > 0
  )

  // Calculate completed exercises
  const completedExerciseCount =
    workoutSession?.exercises.filter((ex) => ex.sets.length > 0).length || 0

  // Show success screen
  if (showSuccess) {
    return <WorkoutSuccessScreen exerciseCount={completedExerciseCount} />
  }

  // Pre-workout state
  if (!workoutSession) {
    return (
      <div className="min-h-[100dvh] bg-bg-primary p-4">
        <div className="mx-auto max-w-lg">
          <div className="mb-6 rounded-lg bg-bg-secondary p-6 shadow-theme-md">
            <h1 className="mb-2 text-2xl font-bold text-text-primary">
              {currentWorkout.Label}
            </h1>
            <p className="mb-4 text-text-secondary">
              {totalExercises} exercises
            </p>
            <p className="text-sm text-text-tertiary">
              Ready to start your workout
            </p>
          </div>

          <button
            onClick={handleStartWorkout}
            className="w-full py-4 min-h-[56px] bg-gradient-metallic-gold text-text-inverse font-semibold text-lg tracking-wider rounded-theme shadow-theme-md hover:shadow-theme-lg hover:shadow-xl hover:shadow-brand-primary/20 transition-all shimmer-hover text-shadow-sm"
          >
            Start Workout
          </button>
        </div>
      </div>
    )
  }

  // Active workout state
  return (
    <div className="min-h-[100dvh] bg-bg-primary">
      {/* Header */}
      <div className="bg-white p-4 shadow-sm">
        <div className="mx-auto max-w-lg">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold">{currentWorkout.Label}</h1>
            {isOffline && (
              <span className="rounded-full bg-yellow-100 px-3 py-1 text-sm text-yellow-800">
                Offline Mode
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600">
            Exercise {adjustedExerciseNumber} of {totalExercises}
          </p>
        </div>
      </div>

      {/* Timer */}
      <div className="mx-auto max-w-lg p-4">
        <ExerciseTimer
          restDuration={90}
          onRestComplete={() => {}}
          workoutStartTime={workoutSession.startTime}
          isExercising
        />
      </div>

      {/* Current Exercise */}
      {currentExercise && (
        <div className="mx-auto max-w-lg px-4">
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-2 text-xl font-bold">{currentExercise.Label}</h2>
            <p className="mb-4 text-gray-600">Set {currentSetIndex + 1}</p>

            <div className="mb-4 space-y-2">
              <p className="text-sm text-gray-600">Target: 8-12 reps</p>
              {recommendation && (
                <>
                  <p className="text-sm font-medium text-blue-600">
                    Recommendation:{' '}
                    {recommendation.Weight?.Lb && recommendation.Weight.Lb > 0
                      ? `${formatRecommendationWeight(recommendation.Weight, 'lbs', recommendation.Increments?.Lb)} × ${recommendation.Reps} reps`
                      : `${recommendation.Reps} reps`}
                  </p>
                  {recommendation.RIR && (
                    <p className="text-sm text-blue-600">
                      Target RIR: {recommendation.RIR}
                    </p>
                  )}
                </>
              )}
            </div>

            <div className="flex gap-2">
              <button
                onClick={handleSkipExercise}
                className="flex-1 rounded-lg border border-gray-300 py-3 text-gray-700 hover:bg-gray-50"
              >
                Skip Exercise
              </button>
              <button
                onClick={handleSwapExercise}
                className="flex-1 rounded-lg border border-gray-300 py-3 text-gray-700 hover:bg-gray-50"
              >
                Swap Exercise
              </button>
            </div>
          </div>

          {/* Action buttons */}
          <button
            onClick={handleFinishWorkout}
            disabled={!hasCompletedSets || isLoading}
            className={`w-full rounded-lg py-4 text-lg font-semibold text-white transition-colors ${
              hasCompletedSets
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-gray-400 cursor-not-allowed'
            } disabled:opacity-50`}
          >
            {hasCompletedSets
              ? 'Finish and save workout'
              : 'Complete at least one set'}
          </button>
        </div>
      )}

      {/* Messages */}
      {savedOfflineMessage && (
        <div className="fixed bottom-4 left-4 right-4 mx-auto max-w-lg rounded-lg bg-yellow-100 p-3 text-center text-sm text-yellow-800">
          Saved offline. Will sync when connection is restored.
        </div>
      )}

      {/* Modals */}
      {currentExercise && (
        <ExerciseSwapModal
          isOpen={isSwapModalOpen}
          currentExercise={currentExercise}
          workout={currentWorkout}
          onSwap={handleSwap}
          onClose={() => setIsSwapModalOpen(false)}
        />
      )}
    </div>
  )
}
