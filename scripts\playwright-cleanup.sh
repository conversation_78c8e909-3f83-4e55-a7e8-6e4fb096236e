#!/bin/bash

# Playwright MCP Browser Lock Cleanup Script
# This script cleans up locked browser profiles and zombie processes
# that can prevent Playwright MCP from starting properly

echo "🧹 Cleaning up Playwright browser locks..."

# Kill any existing Playwright MCP server processes
if pgrep -f "mcp-server-playwright" > /dev/null; then
    echo "  → Stopping Playwright MCP server processes..."
    pkill -9 -f "mcp-server-playwright" 2>/dev/null
    pkill -9 -f "@playwright/mcp" 2>/dev/null
    sleep 1
fi

# Remove locked browser profiles
PROFILE_PATHS=(
    "$HOME/.cache/ms-playwright/mcp-chrome-profile"
    "/root/.cache/ms-playwright/mcp-chrome-profile"
    "/tmp/playwright-mcp-*"
)

for profile in "${PROFILE_PATHS[@]}"; do
    if [ -e "$profile" ]; then
        echo "  → Removing locked profile: $profile"
        rm -rf "$profile" 2>/dev/null
    fi
done

# Kill any zombie Chrome/Chromium processes
if pgrep -f "chrome|chromium" > /dev/null; then
    echo "  → Cleaning up zombie browser processes..."
    pkill -9 -f "chrome" 2>/dev/null
    pkill -9 -f "chromium" 2>/dev/null
fi

# Clear any port locks (Chrome debug port)
if lsof -i :9222 > /dev/null 2>&1; then
    echo "  → Clearing port 9222..."
    lsof -ti :9222 | xargs -r kill -9 2>/dev/null
fi

# Wait for cleanup to complete
sleep 2

echo "✅ Playwright cleanup complete!"
echo ""
echo "ℹ️  The MCP server will restart automatically when needed."
echo "   If you still encounter issues, try running this script again."